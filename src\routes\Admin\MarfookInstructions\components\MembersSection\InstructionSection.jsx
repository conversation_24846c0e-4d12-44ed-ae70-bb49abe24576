import { useEffect, useState } from 'react';
import {
  Avatar,
  Box,
  Button,
  FormControl,
  Grid,
  IconButton,
  Modal,
  Paper,
  Stack,
  styled,
  SvgIcon,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { Link, useNavigate } from 'react-router-dom';
import {
  Check,
  Close,
  ControlPointDuplicate,
  Delete,
  Edit,
  Key,
  PersonAdd,
  PersonOff,
  PersonRemove,
  RemoveModerator,
  RemoveRedEye,
  VerifiedUser,
  ViewColumn,
} from '@mui/icons-material';
import { ReactComponent as ChatTextIcon } from 'static/icons/ChatText.svg';
import { ReactComponent as BellSimpleIcon } from 'static/icons/BellSimple.svg';
import { useIsDesktop } from '../../../../../utils';
import SearchInput from './components/SearchInput';
import { updateOrganization } from '../../../../../apis/organization';
import BottomSheetMessage from '../../../../../components/BottomSheetMessage/BottomSheetMessage';
import BottomSheetPrimaryButton from '../../../../../components/BottomSheetPrimaryButton/BottomSheetPrimaryButton';
import BottomSheetSecondaryButton from '../../../../../components/BottomSheetSecondaryButton/BottomSheetSecondaryButton';
import BottomSheet from '../../../../../components/BottomSheet/BottomSheet';
import MultiSelectNonAllocatedUsers from './components/MultiSelectNonAllocatedUsers';
import { deleteMarfookInstruction, updateMarfookUser } from 'apis/marfook';
import { useDispatch } from 'react-redux';
import { setSnackbar } from 'store/layout';
import SelectMarfookUser from './components/MultiSelectNonAllocatedUsers';
import { PATHS } from 'constants';

const CTableHead = styled(TableHead)(() => ({
  '& th': {
    fontSize: '14px',
    color: '#737373',
    border: 'none',
  },
}));
const CTableRow = styled(TableRow)(() => ({
  '& td': {
    border: 0,
    color: '#222222',
    fontSize: '16px',
  },
}));
function CActionCell({
  sx,
  actions = [],
  basePath,
  onDelete,
  isDesktop,
  id,
  tooltip,
}) {
  const btnStyle = {
    color: '#222',
    background: '#ECEDF7',
    width: isDesktop ? '35px' : 'calc(33% - 10px)',
    minWidth: '35px',
    height: '35px',
    margin: '0 5px',
    boxShadow: 'none',
    '&:hover': {
      background: '#E7EAF4',
    },
  };

  const actionButtons = [
    {
      action: 'view',
      icon: <RemoveRedEye sx={{ fontSize: '14px' }} />,
      link: !!basePath && basePath !== '#' ? `/${basePath}/view/${id}` : '#',
    },
    // { action: 'view', icon: <RemoveRedEye sx={{ fontSize: '14px' }} />, link: '#' },
    {
      action: 'edit',
      icon: <Edit sx={{ fontSize: '14px' }} />,
      link: basePath ? `/${basePath}/${id}/update/` : '#',
    },
    // { action: 'edit', icon: <Edit sx={{ fontSize: '14px' }} />, link: '#' },
    {
      action: 'delete',
      icon: <Delete sx={{ fontSize: '14px' }} />,
      onClick: () => onDelete(id),
    },
    {
      action: 'removeUser',
      icon: <PersonOff sx={{ fontSize: '20px' }} />,
      onClick: () => onDelete(id),
    },
  ];

  return (
    <TableCell sx={sx} colSpan={isDesktop ? 1 : 2}>
      {actionButtons.map(
        ({ action, icon, link, onClick }) =>
          actions.includes(action) && (
            <Link key={action} to={link} style={{ textDecoration: 'none' }}>
              <Tooltip title={tooltip} placement="top">
                <Button
                  variant="contained"
                  size="small"
                  sx={btnStyle}
                  onClick={onClick}
                >
                  {icon}
                </Button>
              </Tooltip>
            </Link>
          ),
      )}
    </TableCell>
  );
}

function InstructionSection({ list = [] }) {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const [open, setOpen] = useState(false);

  const [instructions, setInstructions] = useState(list);

  const [searchText, setSearchText] = useState('');
  const [filteredData, setFilteredData] = useState(list);
  const [showDeleteBottomSheet, setShowDeleteBottomSheet] = useState(false);
  const [selectedRow, setSelectedRow] = useState(null);

  const [addUserForm, setAddUserForm] = useState({ step: 1 });

  useEffect(() => {
    const filtered = instructions
      .filter(item =>
        item?.title?.toLowerCase().includes(searchText?.toLowerCase()),
      )
      .map(x => ({
        ...x,
      }));

    setFilteredData(filtered);
  }, [searchText, instructions]);
  const handleInputChange = e => {
    setSearchText(e.target.value);
  };
  const handleDelete = item => {
    setShowDeleteBottomSheet(true);
    setSelectedRow(item);
  };

  const handleAddMember = async () => {
    try {
      if (selectedMemberToAdd.id) {
        const response = await updateMarfookUser(selectedMemberToAdd.id, {
          groups: userPermisions.filter(
            x =>
              x === 'MarfookManager' ||
              x === 'MarfookExpert' ||
              x === 'MarfookEvaluator',
          ),
        });
        dispatch(
          setSnackbar({
            message: 'کابر با موفقیت افزوده شد',
            severity: 'success',
          }),
        );
        setMembers(response.data || members);
        // window.location.reload();
      }
    } catch (e) {
      dispatch(
        setSnackbar({
          message: 'خطا در افزودن کاربر!',
          severity: 'error',
        }),
      );
    } finally {
      setAddUserForm({ step: 1 });
      setUserPermisions([]);
      setSelectedMemberToAdd(null);
      setSelectedMember(null);
      setOpen(false);
    }
  };

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setAddUserForm({ step: 1 });
    setSelectedRow(null);
    setOpen(false);
  };

  const handleUserClick = async (type = 'user') => {
    setAddUserForm({ ...addUserForm, type, step: 2 });
  };

  const deleteRow = async () => {
    try {
      if (selectedRow.id) {
        await deleteMarfookInstruction(selectedRow.id);
        setInstructions(
          instructions.filter(item => item.id !== selectedRow.id),
        );
        dispatch(
          setSnackbar({
            message: 'اعلان مورد نظر حذف شد',
            severity: 'success',
          }),
        );
      }
    } catch (e) {
      dispatch(
        setSnackbar({
          message: 'خطا در حذف اعلان!',
          severity: 'error',
        }),
      );
    } finally {
      setShowDeleteBottomSheet(false);
    }
  };

  const colTitles = ['عنوان', 'تاریخ ایجاد', 'تاریخ پایان', 'اقدامات'];

  return (
    <Grid
      sx={{
        py: 2,
        px: 3,
        mb: 3,
        borderRadius: '8px',
        background: 'white',
        boxShadow: '0px 2px 20px 0px #00000012',
        width: '100%',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          width: '100%',
          gap: '10px',
          paddingBottom: '10px',
          borderBottom: '1px solid #ccc',
        }}
      >
        <SearchInput
          label="جستجو"
          onChange={handleInputChange}
          sx={{ maxWidth: '400px', zIndex: '10' }}
        />
        <Button
          variant="contained"
          size="large"
          type="submit"
          sx={{ width: '180px' }}
          onClick={() => {
            navigate(PATHS.admin.marfookInstructionCreate);
          }}
        >
          <BellSimpleIcon sx={{ marginRight: '6px' }} />
          افزودن اعلان
        </Button>
      </Box>
      <TableContainer>
        <Table>
          <CTableHead>
            <TableRow>
              {colTitles.map(title => (
                <TableCell>{title}</TableCell>
              ))}
            </TableRow>
          </CTableHead>
          <TableBody>
            {filteredData.map(row => (
              <CTableRow key={row.id}>
                <TableCell width="100%">
                  <span style={{ fontSize: '16px', fontWeight: 'bold' }}>
                    {row.title}
                  </span>
                </TableCell>
                <TableCell width="100%">
                  <span style={{ fontSize: '16px', fontWeight: 'bold' }}>
                    {row.created_at}
                  </span>
                </TableCell>
                <TableCell width="100%">
                  <span style={{ fontSize: '16px', fontWeight: 'bold' }}>
                    {row.expiration_date}
                  </span>
                </TableCell>

                <TableCell>
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Tooltip title="مشاهده اعلان" placement="top">
                      <IconButton
                        sx={{ display: 'flex' }}
                        onClick={() =>
                          navigate(
                            `/admin-panel/marfook/instructions/${row.id}`,
                          )
                        }
                      >
                        <ViewColumn />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="حذف از اعضای مرفوک" placement="top">
                      <IconButton
                        sx={{ display: 'flex' }}
                        onClick={() => handleDelete(row)}
                      >
                        <Delete />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </TableCell>
              </CTableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <Modal
        open={open}
        onClose={handleClose}
        aria-labelledby="child-modal-title"
        aria-describedby="child-modal-description"
      >
        <Grid
          container
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            pt: 2,
            px: 4,
            pb: 3,
            borderRadius: '8px',
            background: 'white',
            boxShadow: '0px 2px 20px 0px #00000012',
            width: '50%',
          }}
        >
          <Stack width="100%">
            <Typography
              align="center"
              fontWeight="bold"
              fontSize={18}
              sx={{ pb: 2, borderBottom: '1px solid #ccc' }}
            >
              افزودن اعلان
            </Typography>
          </Stack>
        </Grid>
      </Modal>

      <BottomSheet
        title="حذف اعلان"
        hideBottomSheet={() => setShowDeleteBottomSheet(false)}
        show={showDeleteBottomSheet}
      >
        <BottomSheetMessage>
          آیا می‌خواهید این اعلان را حذف کنید؟
        </BottomSheetMessage>

        <Stack direction="row">
          <BottomSheetPrimaryButton onClick={() => deleteRow()}>
            بله
          </BottomSheetPrimaryButton>
          <BottomSheetSecondaryButton
            onClick={() => setShowDeleteBottomSheet(false)}
          >
            خیر
          </BottomSheetSecondaryButton>
        </Stack>
      </BottomSheet>
    </Grid>
  );
}

export default InstructionSection;
